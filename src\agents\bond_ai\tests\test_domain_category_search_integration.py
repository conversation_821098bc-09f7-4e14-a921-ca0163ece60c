"""Integration tests for domain category search without mocks.

These tests import the tool module directly from its file path (no package
initializers), and exercise the real vector store against the packaged
`domain_categories.txt` file.
"""

from typing import List
import os
import sys
import importlib.util
import pytest

# Require real dependencies instead of mocks; skip if they aren't installed
pytest.importorskip("langchain_core.tools")
pytest.importorskip("langgraph.config")

# Load the module directly to avoid importing the entire tools package
TESTS_DIR = os.path.dirname(__file__)
print(f"TESTS_DIR: {TESTS_DIR}")
MODULE_PATH = os.path.abspath(os.path.join(
    TESTS_DIR, "..",  "tools", "vector_stores.py"
))

spec = importlib.util.spec_from_file_location("domain_category_search", MODULE_PATH)
module = importlib.util.module_from_spec(spec)
assert spec and spec.loader
# Ensure module is discoverable during dataclass processing
sys.modules[spec.name] = module  # type: ignore[arg-type]
spec.loader.exec_module(module)  # type: ignore[union-attr]

DomainCategoryVectorStore = module.DomainCategoryVectorStore  # type: ignore[attr-defined]


## HOW TO EXECUTE AND DISPLAY THE PRINT STATEMENTS
## pytest src\agents\bond_ai\tests\test_domain_category_search_integration.py -v -s

def test_vector_store_returns_technology_for_tech_queries():
    store = DomainCategoryVectorStore()
    queries: List[str] = [
        "tech companies",
        "technology",
        "software companies",
        "computer industry",
        "saas industry",
        "cloud computing",
        "recruiment companies",
        "saas",
        "tech"
    ]

    for q in queries:
        results = store.query(q, top_k=3)
        assert len(results) == 3
        print(f"Query: {q} -> {[f'{result[0].label} ({result[1]:.3f})' for result in results]}")

@pytest.mark.skip("THIS WAS JUST FOR THE NEW FORMAT")
def test_vector_store_json_format():
    """Test that the vector store works with the new JSON format."""
    try:
        store = DomainCategoryVectorStore()
        print(f"JSON format: Loaded {len(store.categories)} categories")

        # Test that we can query and get results
        results = store.query("technology software", top_k=3)
        assert len(results) == 3
        print(f"JSON Query: technology software -> {[f'{result[0].label} ({result[1]:.3f})' for result in results]}")

        # Test that categories have the expected structure
        first_cat = store.categories[0]
        assert hasattr(first_cat, 'industry_id')
        assert hasattr(first_cat, 'label')
        assert hasattr(first_cat, 'hierarchy')
        assert hasattr(first_cat, 'description')

        # Test specific queries that should work well with the new data
        test_queries = [
            "financial services",
            "healthcare medical",
            "manufacturing",
            "education"
        ]

        for query in test_queries:
            results = store.query(query, top_k=2)
            assert len(results) == 2
            print(f"JSON Query: {query} -> {[f'{result[0].label} ({result[1]:.3f})' for result in results]}")

    except FileNotFoundError:
        # If JSON file doesn't exist, skip this test
        pytest.skip("JSON industries file not found")



